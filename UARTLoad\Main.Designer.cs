﻿namespace UARTLoad
{
    partial class Main
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Main));
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.txt_recvdata = new System.Windows.Forms.TextBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.btn_fileOpen = new System.Windows.Forms.Button();
            this.txt_fileName = new System.Windows.Forms.TextBox();
            this.btn_fileDownload = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.com_portName = new System.Windows.Forms.ComboBox();
            this.btn_sysConn = new System.Windows.Forms.Button();
            this.btn_openPort = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.txt_sysVision = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.btn_readVision = new System.Windows.Forms.Button();
            this.btn_setConfig = new System.Windows.Forms.Button();
            this.btn_readConfig = new System.Windows.Forms.Button();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.label_meterManufacturer = new System.Windows.Forms.Label();
            this.cmb_meterManufacturer = new System.Windows.Forms.ComboBox();
            this.label_baudRate = new System.Windows.Forms.Label();
            this.cmb_baudRate = new System.Windows.Forms.ComboBox();
            this.label_dataBits = new System.Windows.Forms.Label();
            this.cmb_dataBits = new System.Windows.Forms.ComboBox();
            this.label_stopBits = new System.Windows.Forms.Label();
            this.cmb_stopBits = new System.Windows.Forms.ComboBox();
            this.label_parity = new System.Windows.Forms.Label();
            this.cmb_parity = new System.Windows.Forms.ComboBox();
            this.label_collectPeriod = new System.Windows.Forms.Label();
            this.txt_collectPeriod = new System.Windows.Forms.TextBox();
            this.btn_setProtocol = new System.Windows.Forms.Button();
            this.btn_readProtocol = new System.Windows.Forms.Button();
            this.label_reportPeriod1 = new System.Windows.Forms.Label();
            this.txt_reportPeriod1 = new System.Windows.Forms.TextBox();
            this.label_reportPeriod2 = new System.Windows.Forms.Label();
            this.txt_reportPeriod2 = new System.Windows.Forms.TextBox();
            this.label_reportPeriod3 = new System.Windows.Forms.Label();
            this.txt_reportPeriod3 = new System.Windows.Forms.TextBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label_dataCompatMode = new System.Windows.Forms.Label();
            this.cmb_dataCompatMode = new System.Windows.Forms.ComboBox();
            this.btn_setOtherConfig = new System.Windows.Forms.Button();
            this.btn_readOtherConfig = new System.Windows.Forms.Button();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.label_terminalID = new System.Windows.Forms.Label();
            this.txt_terminalID = new System.Windows.Forms.TextBox();
            this.btn_setTerminalID = new System.Windows.Forms.Button();
            this.btn_readTerminalID = new System.Windows.Forms.Button();
            this.btn_setIP1 = new System.Windows.Forms.Button();
            this.btn_readIP1 = new System.Windows.Forms.Button();
            this.btn_setIP2 = new System.Windows.Forms.Button();
            this.btn_readIP2 = new System.Windows.Forms.Button();
            this.btn_setIP3 = new System.Windows.Forms.Button();
            this.btn_readIP3 = new System.Windows.Forms.Button();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.label_centerIP1 = new System.Windows.Forms.Label();
            this.txt_centerIP1 = new System.Windows.Forms.TextBox();
            this.label_centerPort1 = new System.Windows.Forms.Label();
            this.txt_centerPort1 = new System.Windows.Forms.TextBox();
            this.label_centerIP2 = new System.Windows.Forms.Label();
            this.txt_centerIP2 = new System.Windows.Forms.TextBox();
            this.label_centerPort2 = new System.Windows.Forms.Label();
            this.txt_centerPort2 = new System.Windows.Forms.TextBox();
            this.label_centerIP3 = new System.Windows.Forms.Label();
            this.txt_centerIP3 = new System.Windows.Forms.TextBox();
            this.label_centerPort3 = new System.Windows.Forms.Label();
            this.txt_centerPort3 = new System.Windows.Forms.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.groupBox4.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox8.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.SuspendLayout();
            //
            // progressBar
            //
            this.progressBar.Location = new System.Drawing.Point(600, 598);
            this.progressBar.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(421, 25);
            this.progressBar.TabIndex = 4;
            // 
            // txt_recvdata
            // 
            this.txt_recvdata.Location = new System.Drawing.Point(464, 60);
            this.txt_recvdata.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txt_recvdata.Multiline = true;
            this.txt_recvdata.Name = "txt_recvdata";
            this.txt_recvdata.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txt_recvdata.Size = new System.Drawing.Size(104, 233);
            this.txt_recvdata.TabIndex = 5;
            this.txt_recvdata.Visible = false;
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("pictureBox1.BackgroundImage")));
            this.pictureBox1.Location = new System.Drawing.Point(-61, 31);
            this.pictureBox1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(519, 286);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox1.TabIndex = 8;
            this.pictureBox1.TabStop = false;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.btn_fileOpen);
            this.groupBox4.Controls.Add(this.txt_recvdata);
            this.groupBox4.Controls.Add(this.txt_fileName);
            this.groupBox4.Controls.Add(this.btn_fileDownload);
            this.groupBox4.Controls.Add(this.pictureBox1);
            this.groupBox4.Location = new System.Drawing.Point(600, 5);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox4.Size = new System.Drawing.Size(421, 463);
            this.groupBox4.TabIndex = 9;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "文件下载";
            // 
            // btn_fileOpen
            // 
            this.btn_fileOpen.BackColor = System.Drawing.SystemColors.HighlightText;
            this.btn_fileOpen.Location = new System.Drawing.Point(20, 336);
            this.btn_fileOpen.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btn_fileOpen.Name = "btn_fileOpen";
            this.btn_fileOpen.Size = new System.Drawing.Size(87, 33);
            this.btn_fileOpen.TabIndex = 11;
            this.btn_fileOpen.Text = "打开文件";
            this.btn_fileOpen.UseVisualStyleBackColor = false;
            this.btn_fileOpen.Click += new System.EventHandler(this.btn_fileOpen_Click);
            // 
            // txt_fileName
            // 
            this.txt_fileName.Location = new System.Drawing.Point(120, 336);
            this.txt_fileName.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txt_fileName.Multiline = true;
            this.txt_fileName.Name = "txt_fileName";
            this.txt_fileName.Size = new System.Drawing.Size(295, 33);
            this.txt_fileName.TabIndex = 10;
            // 
            // btn_fileDownload
            // 
            this.btn_fileDownload.BackColor = System.Drawing.SystemColors.HighlightText;
            this.btn_fileDownload.Enabled = false;
            this.btn_fileDownload.Font = new System.Drawing.Font("Microsoft Sans Serif", 18F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_fileDownload.Location = new System.Drawing.Point(138, 386);
            this.btn_fileDownload.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btn_fileDownload.Name = "btn_fileDownload";
            this.btn_fileDownload.Size = new System.Drawing.Size(150, 50);
            this.btn_fileDownload.TabIndex = 9;
            this.btn_fileDownload.Text = "下载";
            this.btn_fileDownload.UseVisualStyleBackColor = false;
            this.btn_fileDownload.Click += new System.EventHandler(this.btn_fileDownload_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox1.Controls.Add(this.com_portName);
            this.groupBox1.Controls.Add(this.btn_sysConn);
            this.groupBox1.Controls.Add(this.btn_openPort);
            this.groupBox1.Location = new System.Drawing.Point(5, 5);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox1.Size = new System.Drawing.Size(580, 90);
            this.groupBox1.TabIndex = 13;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "串口连接";
            // 
            // com_portName
            // 
            this.com_portName.FormattingEnabled = true;
            this.com_portName.ItemHeight = 15;
            this.com_portName.Items.AddRange(new object[] {
            "COM1",
            "COM2"});
            this.com_portName.Location = new System.Drawing.Point(20, 35);
            this.com_portName.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.com_portName.Name = "com_portName";
            this.com_portName.Size = new System.Drawing.Size(120, 23);
            this.com_portName.TabIndex = 1;
            this.com_portName.Text = "COM1";
            this.com_portName.DropDown += new System.EventHandler(this.com_portName_DropDown);
            // 
            // btn_sysConn
            // 
            this.btn_sysConn.Enabled = false;
            this.btn_sysConn.Location = new System.Drawing.Point(300, 30);
            this.btn_sysConn.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btn_sysConn.Name = "btn_sysConn";
            this.btn_sysConn.Size = new System.Drawing.Size(120, 35);
            this.btn_sysConn.TabIndex = 7;
            this.btn_sysConn.Text = "连接";
            this.btn_sysConn.UseVisualStyleBackColor = true;
            this.btn_sysConn.Click += new System.EventHandler(this.btn_sysConn_Click);
            // 
            // btn_openPort
            // 
            this.btn_openPort.Location = new System.Drawing.Point(160, 30);
            this.btn_openPort.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btn_openPort.Name = "btn_openPort";
            this.btn_openPort.Size = new System.Drawing.Size(120, 35);
            this.btn_openPort.TabIndex = 6;
            this.btn_openPort.Text = "打开";
            this.btn_openPort.UseVisualStyleBackColor = true;
            this.btn_openPort.Click += new System.EventHandler(this.btn_openPort_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox2.Controls.Add(this.txt_sysVision);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.btn_readVision);
            this.groupBox2.Location = new System.Drawing.Point(5, 105);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox2.Size = new System.Drawing.Size(580, 80);
            this.groupBox2.TabIndex = 14;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "系统版本";
            // 
            // txt_sysVision
            // 
            this.txt_sysVision.Location = new System.Drawing.Point(110, 32);
            this.txt_sysVision.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txt_sysVision.Name = "txt_sysVision";
            this.txt_sysVision.Size = new System.Drawing.Size(200, 21);
            this.txt_sysVision.TabIndex = 14;
            this.txt_sysVision.TextChanged += new System.EventHandler(this.txt_sysVision_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(20, 35);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(82, 16);
            this.label2.TabIndex = 13;
            this.label2.Text = "系统版本：";
            // 
            // btn_readVision
            // 
            this.btn_readVision.Enabled = false;
            this.btn_readVision.Location = new System.Drawing.Point(330, 25);
            this.btn_readVision.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btn_readVision.Name = "btn_readVision";
            this.btn_readVision.Size = new System.Drawing.Size(120, 35);
            this.btn_readVision.TabIndex = 10;
            this.btn_readVision.Text = "读取";
            this.btn_readVision.UseVisualStyleBackColor = true;
            this.btn_readVision.Click += new System.EventHandler(this.btn_readVision_Click);
            // 
            // btn_setConfig
            // 
            this.btn_setConfig.Location = new System.Drawing.Point(0, 0);
            this.btn_setConfig.Name = "btn_setConfig";
            this.btn_setConfig.Size = new System.Drawing.Size(75, 23);
            this.btn_setConfig.TabIndex = 0;
            // 
            // btn_readConfig
            // 
            this.btn_readConfig.Location = new System.Drawing.Point(0, 0);
            this.btn_readConfig.Name = "btn_readConfig";
            this.btn_readConfig.Size = new System.Drawing.Size(75, 23);
            this.btn_readConfig.TabIndex = 0;
            // 
            // groupBox6
            // 
            this.groupBox6.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox6.Controls.Add(this.label_meterManufacturer);
            this.groupBox6.Controls.Add(this.cmb_meterManufacturer);
            this.groupBox6.Controls.Add(this.label_baudRate);
            this.groupBox6.Controls.Add(this.cmb_baudRate);
            this.groupBox6.Controls.Add(this.label_dataBits);
            this.groupBox6.Controls.Add(this.cmb_dataBits);
            this.groupBox6.Controls.Add(this.label_stopBits);
            this.groupBox6.Controls.Add(this.cmb_stopBits);
            this.groupBox6.Controls.Add(this.label_parity);
            this.groupBox6.Controls.Add(this.cmb_parity);
            this.groupBox6.Controls.Add(this.label_collectPeriod);
            this.groupBox6.Controls.Add(this.txt_collectPeriod);
            this.groupBox6.Controls.Add(this.btn_setProtocol);
            this.groupBox6.Controls.Add(this.btn_readProtocol);
            this.groupBox6.Location = new System.Drawing.Point(5, 332);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox6.Size = new System.Drawing.Size(580, 140);
            this.groupBox6.TabIndex = 17;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "厂家协议配置";
            // 
            // label_meterManufacturer
            // 
            this.label_meterManufacturer.AutoSize = true;
            this.label_meterManufacturer.Location = new System.Drawing.Point(20, 30);
            this.label_meterManufacturer.Name = "label_meterManufacturer";
            this.label_meterManufacturer.Size = new System.Drawing.Size(70, 16);
            this.label_meterManufacturer.TabIndex = 16;
            this.label_meterManufacturer.Text = "水表厂家:";
            this.label_meterManufacturer.Click += new System.EventHandler(this.label_meterManufacturer_Click);
            // 
            // cmb_meterManufacturer
            // 
            this.cmb_meterManufacturer.FormattingEnabled = true;
            this.cmb_meterManufacturer.Items.AddRange(new object[] {
            "大连道盛",
            "泰安",
            "唐山",
            "河南"});
            this.cmb_meterManufacturer.Location = new System.Drawing.Point(100, 27);
            this.cmb_meterManufacturer.Name = "cmb_meterManufacturer";
            this.cmb_meterManufacturer.Size = new System.Drawing.Size(150, 23);
            this.cmb_meterManufacturer.TabIndex = 17;
            this.cmb_meterManufacturer.Text = "大连道盛";
            this.cmb_meterManufacturer.SelectedIndexChanged += new System.EventHandler(this.cmb_meterManufacturer_SelectedIndexChanged);
            // 
            // label_baudRate
            // 
            this.label_baudRate.AutoSize = true;
            this.label_baudRate.Location = new System.Drawing.Point(20, 65);
            this.label_baudRate.Name = "label_baudRate";
            this.label_baudRate.Size = new System.Drawing.Size(55, 16);
            this.label_baudRate.TabIndex = 18;
            this.label_baudRate.Text = "波特率:";
            // 
            // cmb_baudRate
            // 
            this.cmb_baudRate.FormattingEnabled = true;
            this.cmb_baudRate.Items.AddRange(new object[] {
            "1200",
            "2400",
            "4800",
            "9600",
            "19200",
            "38400"});
            this.cmb_baudRate.Location = new System.Drawing.Point(80, 62);
            this.cmb_baudRate.Name = "cmb_baudRate";
            this.cmb_baudRate.Size = new System.Drawing.Size(80, 23);
            this.cmb_baudRate.TabIndex = 19;
            this.cmb_baudRate.Text = "9600";
            this.cmb_baudRate.SelectedIndexChanged += new System.EventHandler(this.cmb_baudRate_SelectedIndexChanged);
            // 
            // label_dataBits
            // 
            this.label_dataBits.AutoSize = true;
            this.label_dataBits.Location = new System.Drawing.Point(180, 65);
            this.label_dataBits.Name = "label_dataBits";
            this.label_dataBits.Size = new System.Drawing.Size(55, 16);
            this.label_dataBits.TabIndex = 20;
            this.label_dataBits.Text = "数据位:";
            // 
            // cmb_dataBits
            // 
            this.cmb_dataBits.FormattingEnabled = true;
            this.cmb_dataBits.Items.AddRange(new object[] {
            "7",
            "8",
            "9"});
            this.cmb_dataBits.Location = new System.Drawing.Point(240, 62);
            this.cmb_dataBits.Name = "cmb_dataBits";
            this.cmb_dataBits.Size = new System.Drawing.Size(60, 23);
            this.cmb_dataBits.TabIndex = 21;
            this.cmb_dataBits.Text = "8";
            // 
            // label_stopBits
            // 
            this.label_stopBits.AutoSize = true;
            this.label_stopBits.Location = new System.Drawing.Point(320, 65);
            this.label_stopBits.Name = "label_stopBits";
            this.label_stopBits.Size = new System.Drawing.Size(55, 16);
            this.label_stopBits.TabIndex = 22;
            this.label_stopBits.Text = "停止位:";
            // 
            // cmb_stopBits
            // 
            this.cmb_stopBits.FormattingEnabled = true;
            this.cmb_stopBits.Items.AddRange(new object[] {
            "1",
            "2"});
            this.cmb_stopBits.Location = new System.Drawing.Point(380, 62);
            this.cmb_stopBits.Name = "cmb_stopBits";
            this.cmb_stopBits.Size = new System.Drawing.Size(60, 23);
            this.cmb_stopBits.TabIndex = 23;
            this.cmb_stopBits.Text = "1";
            // 
            // label_parity
            // 
            this.label_parity.AutoSize = true;
            this.label_parity.Location = new System.Drawing.Point(20, 100);
            this.label_parity.Name = "label_parity";
            this.label_parity.Size = new System.Drawing.Size(55, 16);
            this.label_parity.TabIndex = 24;
            this.label_parity.Text = "校验位:";
            // 
            // cmb_parity
            // 
            this.cmb_parity.FormattingEnabled = true;
            this.cmb_parity.Items.AddRange(new object[] {
            "无校验",
            "奇校验",
            "偶校验"});
            this.cmb_parity.Location = new System.Drawing.Point(80, 97);
            this.cmb_parity.Name = "cmb_parity";
            this.cmb_parity.Size = new System.Drawing.Size(80, 23);
            this.cmb_parity.TabIndex = 25;
            this.cmb_parity.Text = "无校验";
            // 
            // label_collectPeriod
            // 
            this.label_collectPeriod.AutoSize = true;
            this.label_collectPeriod.Location = new System.Drawing.Point(180, 100);
            this.label_collectPeriod.Name = "label_collectPeriod";
            this.label_collectPeriod.Size = new System.Drawing.Size(85, 16);
            this.label_collectPeriod.TabIndex = 26;
            this.label_collectPeriod.Text = "采集周期(s):";
            // 
            // txt_collectPeriod
            // 
            this.txt_collectPeriod.Location = new System.Drawing.Point(260, 97);
            this.txt_collectPeriod.Name = "txt_collectPeriod";
            this.txt_collectPeriod.Size = new System.Drawing.Size(80, 21);
            this.txt_collectPeriod.TabIndex = 27;
            this.txt_collectPeriod.Text = "30";
            // 
            // btn_setProtocol
            // 
            this.btn_setProtocol.Enabled = false;
            this.btn_setProtocol.Location = new System.Drawing.Point(360, 92);
            this.btn_setProtocol.Name = "btn_setProtocol";
            this.btn_setProtocol.Size = new System.Drawing.Size(90, 30);
            this.btn_setProtocol.TabIndex = 28;
            this.btn_setProtocol.Text = "设置协议";
            this.btn_setProtocol.UseVisualStyleBackColor = true;
            this.btn_setProtocol.Click += new System.EventHandler(this.btn_setProtocol_Click);
            // 
            // btn_readProtocol
            // 
            this.btn_readProtocol.Enabled = false;
            this.btn_readProtocol.Location = new System.Drawing.Point(470, 92);
            this.btn_readProtocol.Name = "btn_readProtocol";
            this.btn_readProtocol.Size = new System.Drawing.Size(90, 30);
            this.btn_readProtocol.TabIndex = 29;
            this.btn_readProtocol.Text = "读取协议";
            this.btn_readProtocol.UseVisualStyleBackColor = true;
            this.btn_readProtocol.Click += new System.EventHandler(this.btn_readProtocol_Click);
            // 
            // label_reportPeriod1
            // 
            this.label_reportPeriod1.AutoSize = true;
            this.label_reportPeriod1.Location = new System.Drawing.Point(331, 25);
            this.label_reportPeriod1.Name = "label_reportPeriod1";
            this.label_reportPeriod1.Size = new System.Drawing.Size(93, 16);
            this.label_reportPeriod1.TabIndex = 18;
            this.label_reportPeriod1.Text = "上报周期(分):";
            // 
            // txt_reportPeriod1
            // 
            this.txt_reportPeriod1.Location = new System.Drawing.Point(415, 22);
            this.txt_reportPeriod1.Name = "txt_reportPeriod1";
            this.txt_reportPeriod1.Size = new System.Drawing.Size(40, 21);
            this.txt_reportPeriod1.TabIndex = 19;
            this.txt_reportPeriod1.Text = "1";
            // 
            // label_reportPeriod2
            // 
            this.label_reportPeriod2.AutoSize = true;
            this.label_reportPeriod2.Location = new System.Drawing.Point(331, 58);
            this.label_reportPeriod2.Name = "label_reportPeriod2";
            this.label_reportPeriod2.Size = new System.Drawing.Size(93, 16);
            this.label_reportPeriod2.TabIndex = 20;
            this.label_reportPeriod2.Text = "上报周期(分):";
            // 
            // txt_reportPeriod2
            // 
            this.txt_reportPeriod2.Location = new System.Drawing.Point(415, 55);
            this.txt_reportPeriod2.Name = "txt_reportPeriod2";
            this.txt_reportPeriod2.Size = new System.Drawing.Size(40, 21);
            this.txt_reportPeriod2.TabIndex = 21;
            this.txt_reportPeriod2.Text = "2";
            // 
            // label_reportPeriod3
            // 
            this.label_reportPeriod3.AutoSize = true;
            this.label_reportPeriod3.Location = new System.Drawing.Point(331, 93);
            this.label_reportPeriod3.Name = "label_reportPeriod3";
            this.label_reportPeriod3.Size = new System.Drawing.Size(93, 16);
            this.label_reportPeriod3.TabIndex = 22;
            this.label_reportPeriod3.Text = "上报周期(分):";
            // 
            // txt_reportPeriod3
            // 
            this.txt_reportPeriod3.Location = new System.Drawing.Point(415, 90);
            this.txt_reportPeriod3.Name = "txt_reportPeriod3";
            this.txt_reportPeriod3.Size = new System.Drawing.Size(40, 21);
            this.txt_reportPeriod3.TabIndex = 23;
            this.txt_reportPeriod3.Text = "3";
            // 
            // groupBox7
            //
            this.groupBox7.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox7.Controls.Add(this.label_dataCompatMode);
            this.groupBox7.Controls.Add(this.cmb_dataCompatMode);
            this.groupBox7.Controls.Add(this.btn_setOtherConfig);
            this.groupBox7.Controls.Add(this.btn_readOtherConfig);
            this.groupBox7.Location = new System.Drawing.Point(6, 483);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(579, 80);
            this.groupBox7.TabIndex = 7;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "其他配置";
            // 
            // label_dataCompatMode
            // 
            this.label_dataCompatMode.AutoSize = true;
            this.label_dataCompatMode.Location = new System.Drawing.Point(15, 30);
            this.label_dataCompatMode.Name = "label_dataCompatMode";
            this.label_dataCompatMode.Size = new System.Drawing.Size(100, 16);
            this.label_dataCompatMode.TabIndex = 0;
            this.label_dataCompatMode.Text = "监听模式:";
            // 
            // cmb_dataCompatMode
            // 
            this.cmb_dataCompatMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmb_dataCompatMode.FormattingEnabled = true;
            this.cmb_dataCompatMode.Items.AddRange(new object[] {
            "关闭",
            "开启"});
            this.cmb_dataCompatMode.Location = new System.Drawing.Point(115, 27);
            this.cmb_dataCompatMode.Name = "cmb_dataCompatMode";
            this.cmb_dataCompatMode.Size = new System.Drawing.Size(80, 23);
            this.cmb_dataCompatMode.Text = "关闭";
            this.cmb_dataCompatMode.TabIndex = 1;
            // 
            // btn_setOtherConfig
            // 
            this.btn_setOtherConfig.Location = new System.Drawing.Point(220, 25);
            this.btn_setOtherConfig.Name = "btn_setOtherConfig";
            this.btn_setOtherConfig.Size = new System.Drawing.Size(80, 28);
            this.btn_setOtherConfig.TabIndex = 2;
            this.btn_setOtherConfig.Text = "设置";
            this.btn_setOtherConfig.Enabled = false;
            this.btn_setOtherConfig.UseVisualStyleBackColor = true;
            this.btn_setOtherConfig.Click += new System.EventHandler(this.btn_setOtherConfig_Click);
            // 
            // btn_readOtherConfig
            // 
            this.btn_readOtherConfig.Location = new System.Drawing.Point(320, 25);
            this.btn_readOtherConfig.Name = "btn_readOtherConfig";
            this.btn_readOtherConfig.Size = new System.Drawing.Size(80, 28);
            this.btn_readOtherConfig.TabIndex = 3;
            this.btn_readOtherConfig.Text = "读取";
            this.btn_readOtherConfig.Enabled = false;
            this.btn_readOtherConfig.UseVisualStyleBackColor = true;
            this.btn_readOtherConfig.Click += new System.EventHandler(this.btn_readOtherConfig_Click);
            //
            // groupBox8
            //
            this.groupBox8.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox8.Controls.Add(this.label_terminalID);
            this.groupBox8.Controls.Add(this.txt_terminalID);
            this.groupBox8.Controls.Add(this.btn_setTerminalID);
            this.groupBox8.Controls.Add(this.btn_readTerminalID);
            this.groupBox8.Location = new System.Drawing.Point(6, 573);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(579, 80);
            this.groupBox8.TabIndex = 8;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "终端ID配置";
            //
            // label_terminalID
            //
            this.label_terminalID.AutoSize = true;
            this.label_terminalID.Location = new System.Drawing.Point(15, 30);
            this.label_terminalID.Name = "label_terminalID";
            this.label_terminalID.Size = new System.Drawing.Size(70, 16);
            this.label_terminalID.TabIndex = 0;
            this.label_terminalID.Text = "终端ID:";
            //
            // txt_terminalID
            //
            this.txt_terminalID.Location = new System.Drawing.Point(85, 27);
            this.txt_terminalID.MaxLength = 11;
            this.txt_terminalID.Name = "txt_terminalID";
            this.txt_terminalID.Size = new System.Drawing.Size(120, 21);
            this.txt_terminalID.TabIndex = 1;
            this.txt_terminalID.Text = "";
            //
            // btn_setTerminalID
            //
            this.btn_setTerminalID.Enabled = false;
            this.btn_setTerminalID.Location = new System.Drawing.Point(220, 25);
            this.btn_setTerminalID.Name = "btn_setTerminalID";
            this.btn_setTerminalID.Size = new System.Drawing.Size(80, 28);
            this.btn_setTerminalID.TabIndex = 2;
            this.btn_setTerminalID.Text = "设置";
            this.btn_setTerminalID.UseVisualStyleBackColor = true;
            this.btn_setTerminalID.Click += new System.EventHandler(this.btn_setTerminalID_Click);
            //
            // btn_readTerminalID
            //
            this.btn_readTerminalID.Enabled = false;
            this.btn_readTerminalID.Location = new System.Drawing.Point(320, 25);
            this.btn_readTerminalID.Name = "btn_readTerminalID";
            this.btn_readTerminalID.Size = new System.Drawing.Size(80, 28);
            this.btn_readTerminalID.TabIndex = 3;
            this.btn_readTerminalID.Text = "读取";
            this.btn_readTerminalID.UseVisualStyleBackColor = true;
            this.btn_readTerminalID.Click += new System.EventHandler(this.btn_readTerminalID_Click);
            //
            // btn_setIP1
            // 
            this.btn_setIP1.Enabled = false;
            this.btn_setIP1.Location = new System.Drawing.Point(459, 20);
            this.btn_setIP1.Name = "btn_setIP1";
            this.btn_setIP1.Size = new System.Drawing.Size(50, 25);
            this.btn_setIP1.TabIndex = 4;
            this.btn_setIP1.Text = "设置";
            this.btn_setIP1.UseVisualStyleBackColor = true;
            this.btn_setIP1.Click += new System.EventHandler(this.btn_setIP1_Click);
            // 
            // btn_readIP1
            // 
            this.btn_readIP1.Enabled = false;
            this.btn_readIP1.Location = new System.Drawing.Point(519, 20);
            this.btn_readIP1.Name = "btn_readIP1";
            this.btn_readIP1.Size = new System.Drawing.Size(50, 25);
            this.btn_readIP1.TabIndex = 5;
            this.btn_readIP1.Text = "读取";
            this.btn_readIP1.UseVisualStyleBackColor = true;
            this.btn_readIP1.Click += new System.EventHandler(this.btn_readIP1_Click);
            // 
            // btn_setIP2
            // 
            this.btn_setIP2.Enabled = false;
            this.btn_setIP2.Location = new System.Drawing.Point(459, 53);
            this.btn_setIP2.Name = "btn_setIP2";
            this.btn_setIP2.Size = new System.Drawing.Size(50, 25);
            this.btn_setIP2.TabIndex = 8;
            this.btn_setIP2.Text = "设置";
            this.btn_setIP2.UseVisualStyleBackColor = true;
            this.btn_setIP2.Click += new System.EventHandler(this.btn_setIP2_Click);
            // 
            // btn_readIP2
            // 
            this.btn_readIP2.Enabled = false;
            this.btn_readIP2.Location = new System.Drawing.Point(519, 53);
            this.btn_readIP2.Name = "btn_readIP2";
            this.btn_readIP2.Size = new System.Drawing.Size(50, 25);
            this.btn_readIP2.TabIndex = 9;
            this.btn_readIP2.Text = "读取";
            this.btn_readIP2.UseVisualStyleBackColor = true;
            this.btn_readIP2.Click += new System.EventHandler(this.btn_readIP2_Click);
            // 
            // btn_setIP3
            // 
            this.btn_setIP3.Enabled = false;
            this.btn_setIP3.Location = new System.Drawing.Point(459, 88);
            this.btn_setIP3.Name = "btn_setIP3";
            this.btn_setIP3.Size = new System.Drawing.Size(50, 25);
            this.btn_setIP3.TabIndex = 12;
            this.btn_setIP3.Text = "设置";
            this.btn_setIP3.UseVisualStyleBackColor = true;
            this.btn_setIP3.Click += new System.EventHandler(this.btn_setIP3_Click);
            // 
            // btn_readIP3
            // 
            this.btn_readIP3.Enabled = false;
            this.btn_readIP3.Location = new System.Drawing.Point(519, 88);
            this.btn_readIP3.Name = "btn_readIP3";
            this.btn_readIP3.Size = new System.Drawing.Size(50, 25);
            this.btn_readIP3.TabIndex = 13;
            this.btn_readIP3.Text = "读取";
            this.btn_readIP3.UseVisualStyleBackColor = true;
            this.btn_readIP3.Click += new System.EventHandler(this.btn_readIP3_Click);
            // 
            // groupBox5
            // 
            this.groupBox5.BackColor = System.Drawing.SystemColors.MenuHighlight;
            this.groupBox5.Controls.Add(this.label_centerIP1);
            this.groupBox5.Controls.Add(this.txt_centerIP1);
            this.groupBox5.Controls.Add(this.label_centerPort1);
            this.groupBox5.Controls.Add(this.txt_centerPort1);
            this.groupBox5.Controls.Add(this.label_centerIP2);
            this.groupBox5.Controls.Add(this.txt_centerIP2);
            this.groupBox5.Controls.Add(this.label_centerPort2);
            this.groupBox5.Controls.Add(this.txt_centerPort2);
            this.groupBox5.Controls.Add(this.label_centerIP3);
            this.groupBox5.Controls.Add(this.txt_centerIP3);
            this.groupBox5.Controls.Add(this.label_centerPort3);
            this.groupBox5.Controls.Add(this.txt_centerPort3);
            this.groupBox5.Controls.Add(this.btn_setIP1);
            this.groupBox5.Controls.Add(this.btn_readIP1);
            this.groupBox5.Controls.Add(this.btn_setIP2);
            this.groupBox5.Controls.Add(this.btn_readIP2);
            this.groupBox5.Controls.Add(this.btn_setIP3);
            this.groupBox5.Controls.Add(this.btn_readIP3);
            this.groupBox5.Controls.Add(this.label_reportPeriod1);
            this.groupBox5.Controls.Add(this.txt_reportPeriod1);
            this.groupBox5.Controls.Add(this.label_reportPeriod2);
            this.groupBox5.Controls.Add(this.txt_reportPeriod2);
            this.groupBox5.Controls.Add(this.label_reportPeriod3);
            this.groupBox5.Controls.Add(this.txt_reportPeriod3);
            this.groupBox5.Location = new System.Drawing.Point(5, 195);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupBox5.Size = new System.Drawing.Size(580, 127);
            this.groupBox5.TabIndex = 16;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "上级平台配置";
            // 
            // label_centerIP1
            // 
            this.label_centerIP1.AutoSize = true;
            this.label_centerIP1.Location = new System.Drawing.Point(10, 25);
            this.label_centerIP1.Name = "label_centerIP1";
            this.label_centerIP1.Size = new System.Drawing.Size(72, 16);
            this.label_centerIP1.TabIndex = 0;
            this.label_centerIP1.Text = "1.中心1 IP:";
            this.label_centerIP1.Click += new System.EventHandler(this.label_centerIP1_Click);
            // 
            // txt_centerIP1
            // 
            this.txt_centerIP1.Location = new System.Drawing.Point(111, 22);
            this.txt_centerIP1.Name = "txt_centerIP1";
            this.txt_centerIP1.Size = new System.Drawing.Size(120, 21);
            this.txt_centerIP1.TabIndex = 1;
            // 
            // label_centerPort1
            // 
            this.label_centerPort1.AutoSize = true;
            this.label_centerPort1.Location = new System.Drawing.Point(233, 25);
            this.label_centerPort1.Name = "label_centerPort1";
            this.label_centerPort1.Size = new System.Drawing.Size(40, 16);
            this.label_centerPort1.TabIndex = 2;
            this.label_centerPort1.Text = "端口:";
            // 
            // txt_centerPort1
            // 
            this.txt_centerPort1.Location = new System.Drawing.Point(273, 22);
            this.txt_centerPort1.Name = "txt_centerPort1";
            this.txt_centerPort1.Size = new System.Drawing.Size(60, 21);
            this.txt_centerPort1.TabIndex = 3;
            this.txt_centerPort1.TextChanged += new System.EventHandler(this.txt_centerPort1_TextChanged);
            // 
            // label_centerIP2
            // 
            this.label_centerIP2.AutoSize = true;
            this.label_centerIP2.Location = new System.Drawing.Point(10, 58);
            this.label_centerIP2.Name = "label_centerIP2";
            this.label_centerIP2.Size = new System.Drawing.Size(72, 16);
            this.label_centerIP2.TabIndex = 4;
            this.label_centerIP2.Text = "2.中心2 IP:";
            this.label_centerIP2.Click += new System.EventHandler(this.label_centerIP2_Click);
            // 
            // txt_centerIP2
            // 
            this.txt_centerIP2.Location = new System.Drawing.Point(111, 55);
            this.txt_centerIP2.Name = "txt_centerIP2";
            this.txt_centerIP2.Size = new System.Drawing.Size(120, 21);
            this.txt_centerIP2.TabIndex = 5;
            // 
            // label_centerPort2
            // 
            this.label_centerPort2.AutoSize = true;
            this.label_centerPort2.Location = new System.Drawing.Point(233, 58);
            this.label_centerPort2.Name = "label_centerPort2";
            this.label_centerPort2.Size = new System.Drawing.Size(40, 16);
            this.label_centerPort2.TabIndex = 6;
            this.label_centerPort2.Text = "端口:";
            // 
            // txt_centerPort2
            // 
            this.txt_centerPort2.Location = new System.Drawing.Point(273, 55);
            this.txt_centerPort2.Name = "txt_centerPort2";
            this.txt_centerPort2.Size = new System.Drawing.Size(60, 21);
            this.txt_centerPort2.TabIndex = 7;
            // 
            // label_centerIP3
            // 
            this.label_centerIP3.AutoSize = true;
            this.label_centerIP3.Location = new System.Drawing.Point(10, 93);
            this.label_centerIP3.Name = "label_centerIP3";
            this.label_centerIP3.Size = new System.Drawing.Size(72, 16);
            this.label_centerIP3.TabIndex = 8;
            this.label_centerIP3.Text = "3.中心3 IP:";
            this.label_centerIP3.Click += new System.EventHandler(this.label_centerIP3_Click);
            // 
            // txt_centerIP3
            // 
            this.txt_centerIP3.Location = new System.Drawing.Point(111, 90);
            this.txt_centerIP3.Name = "txt_centerIP3";
            this.txt_centerIP3.Size = new System.Drawing.Size(120, 21);
            this.txt_centerIP3.TabIndex = 9;
            this.txt_centerIP3.TextChanged += new System.EventHandler(this.txt_centerIP3_TextChanged);
            // 
            // label_centerPort3
            // 
            this.label_centerPort3.AutoSize = true;
            this.label_centerPort3.Location = new System.Drawing.Point(233, 93);
            this.label_centerPort3.Name = "label_centerPort3";
            this.label_centerPort3.Size = new System.Drawing.Size(40, 16);
            this.label_centerPort3.TabIndex = 10;
            this.label_centerPort3.Text = "端口:";
            // 
            // txt_centerPort3
            // 
            this.txt_centerPort3.Location = new System.Drawing.Point(273, 90);
            this.txt_centerPort3.Name = "txt_centerPort3";
            this.txt_centerPort3.Size = new System.Drawing.Size(60, 21);
            this.txt_centerPort3.TabIndex = 11;
            // 
            // Main
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1050, 666);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox6);
            this.Controls.Add(this.groupBox7);
            this.Controls.Add(this.groupBox8);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.progressBar);
            this.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Pixel, ((byte)(134)));
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.Name = "Main";
            this.Text = "UART-CONFIG-TOOL";
            this.Load += new System.EventHandler(this.FormLoad);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.TextBox txt_recvdata;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Button btn_fileOpen;
        private System.Windows.Forms.TextBox txt_fileName;
        private System.Windows.Forms.Button btn_fileDownload;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox com_portName;
        private System.Windows.Forms.Button btn_sysConn;
        private System.Windows.Forms.Button btn_openPort;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TextBox txt_sysVision;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btn_readVision;

        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Label label_centerIP1;
        private System.Windows.Forms.TextBox txt_centerIP1;
        private System.Windows.Forms.Label label_centerPort1;
        private System.Windows.Forms.TextBox txt_centerPort1;
        private System.Windows.Forms.Label label_centerIP2;
        private System.Windows.Forms.TextBox txt_centerIP2;
        private System.Windows.Forms.Label label_centerPort2;
        private System.Windows.Forms.TextBox txt_centerPort2;
        private System.Windows.Forms.Label label_centerIP3;
        private System.Windows.Forms.TextBox txt_centerIP3;
        private System.Windows.Forms.Label label_centerPort3;
        private System.Windows.Forms.TextBox txt_centerPort3;

        private System.Windows.Forms.Label label_meterManufacturer;
        private System.Windows.Forms.ComboBox cmb_meterManufacturer;
        private System.Windows.Forms.Label label_baudRate;
        private System.Windows.Forms.ComboBox cmb_baudRate;
        private System.Windows.Forms.Label label_dataBits;
        private System.Windows.Forms.ComboBox cmb_dataBits;
        private System.Windows.Forms.Label label_stopBits;
        private System.Windows.Forms.ComboBox cmb_stopBits;
        private System.Windows.Forms.Label label_parity;
        private System.Windows.Forms.ComboBox cmb_parity;
        private System.Windows.Forms.Label label_collectPeriod;
        private System.Windows.Forms.TextBox txt_collectPeriod;
        private System.Windows.Forms.Button btn_setConfig;
        private System.Windows.Forms.Button btn_readConfig;
        private System.Windows.Forms.Label label_reportPeriod1;
        private System.Windows.Forms.TextBox txt_reportPeriod1;
        private System.Windows.Forms.Label label_reportPeriod2;
        private System.Windows.Forms.TextBox txt_reportPeriod2;
        private System.Windows.Forms.Label label_reportPeriod3;
        private System.Windows.Forms.TextBox txt_reportPeriod3;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label_dataCompatMode;
        private System.Windows.Forms.ComboBox cmb_dataCompatMode;
        private System.Windows.Forms.Button btn_setOtherConfig;
        private System.Windows.Forms.Button btn_readOtherConfig;

        // 终端ID配置控件
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.Label label_terminalID;
        private System.Windows.Forms.TextBox txt_terminalID;
        private System.Windows.Forms.Button btn_setTerminalID;
        private System.Windows.Forms.Button btn_readTerminalID;
        private System.Windows.Forms.Button btn_setIP1;
        private System.Windows.Forms.Button btn_readIP1;
        private System.Windows.Forms.Button btn_setIP2;
        private System.Windows.Forms.Button btn_readIP2;
        private System.Windows.Forms.Button btn_setIP3;
        private System.Windows.Forms.Button btn_readIP3;


        private System.Windows.Forms.Button btn_setProtocol;
        private System.Windows.Forms.Button btn_readProtocol;
    }
}

