/**
 * PC协议使用示例
 * 展示如何在STM32项目中集成和使用PC协议处理函数
 */

#include "pc_protocol.h"
#include <stdio.h>
#include <string.h>

// 全局配置变量定义
center_config_t g_center1_config = {
    .ip = {192, 168, 1, 100},
    .port = 8080,
    .period = 1
};

center_config_t g_center2_config = {
    .ip = {10, 0, 0, 1},
    .port = 9000,
    .period = 2
};

center_config_t g_center3_config = {
    .ip = {172, 16, 1, 10},
    .port = 8888,
    .period = 3
};

protocol_config_t g_protocol_config = {
    .manufacturer = MANUFACTURER_DALIAN,
    .baud_rate = BAUD_RATE_9600,
    .data_bits = DATA_BITS_8,
    .stop_bits = STOP_BITS_1,
    .parity = PARITY_NONE
};

other_config_t g_other_config = {
    .compat_mode = COMPAT_MODE_OFF
};

terminal_id_config_t g_terminal_id_config = {
    .terminal_id = 65322311666ULL
};

char g_version[3] = {'1', '2', '3'};  // V1.2.3

/**
 * 发送响应函数实现
 * 在实际项目中，这里应该调用UART发送函数
 */
void send_response(uint8_t* data, int length) {
    // 示例：通过UART发送数据
    // HAL_UART_Transmit(&huart1, data, length, 1000);
    
    // 调试输出
    printf("Send response (%d bytes): ", length);
    for (int i = 0; i < length; i++) {
        printf("%02X ", data[i]);
    }
    printf("\n");
}

/**
 * 保存中心配置到Flash
 */
void save_center_config(uint8_t center_num, center_config_t* config) {
    // 示例：保存到Flash
    // flash_write_center_config(center_num, config);
    printf("Save center%d config: IP=%d.%d.%d.%d, Port=%d, Period=%d\n", 
           center_num, config->ip[0], config->ip[1], config->ip[2], config->ip[3],
           config->port, config->period);
}

/**
 * 保存厂家协议配置到Flash
 */
void save_protocol_config(protocol_config_t* config) {
    // 示例：保存到Flash
    // flash_write_protocol_config(config);
    printf("Save protocol config: Manufacturer=%d, BaudRate=%d, DataBits=%d, StopBits=%d, Parity=%d\n",
           config->manufacturer, config->baud_rate, config->data_bits, config->stop_bits, config->parity);
}

/**
 * 保存其他配置到Flash
 */
void save_other_config(other_config_t* config) {
    // 示例：保存到Flash
    // flash_write_other_config(config);
    printf("Save other config: CompatMode=%d\n", config->compat_mode);
}

/**
 * 保存终端ID配置到Flash
 */
void save_terminal_id_config(terminal_id_config_t* config) {
    // 示例：保存到Flash
    // flash_write_terminal_id_config(config);
    printf("Save terminal ID config: ID=%llu\n", config->terminal_id);
}

/**
 * 从Flash加载所有配置
 */
void load_all_configs(void) {
    // 示例：从Flash加载配置
    // flash_read_center_config(1, &g_center1_config);
    // flash_read_center_config(2, &g_center2_config);
    // flash_read_center_config(3, &g_center3_config);
    // flash_read_protocol_config(&g_protocol_config);
    // flash_read_other_config(&g_other_config);
    // flash_read_terminal_id_config(&g_terminal_id_config);
    printf("Load all configs from Flash\n");
}

/**
 * UART接收中断处理函数示例
 * 在实际项目中，这个函数应该在UART接收中断中调用
 */
void uart_rx_handler(uint8_t* rx_buffer, int rx_length) {
    // 处理接收到的数据包
    int result = pc_protocol_process(rx_length, (char*)rx_buffer);
    
    switch (result) {
        case 0:
            printf("Protocol processed successfully\n");
            break;
        case -1:
            printf("Protocol format error\n");
            break;
        case -2:
            printf("Unsupported command\n");
            break;
        default:
            printf("Unknown error\n");
            break;
    }
}

/**
 * 主函数示例
 */
int main(void) {
    // 初始化系统
    printf("PC Protocol Example\n");
    
    // 加载配置
    load_all_configs();
    
    // 模拟接收到的数据包进行测试
    
    // 测试1：连接命令
    uint8_t connect_cmd[] = {0x7E, 0x02, 0x02, 0xCE};
    printf("\nTest 1: Connect command\n");
    uart_rx_handler(connect_cmd, sizeof(connect_cmd));
    
    // 测试2：版本查询命令
    uint8_t version_cmd[] = {0x7E, 0x02, 0x05, 0xCE};
    printf("\nTest 2: Version query command\n");
    uart_rx_handler(version_cmd, sizeof(version_cmd));
    
    // 测试3：设置中心1命令
    uint8_t set_center1_cmd[] = {
        0x7E, 0x00, 0x09, 0x11,           // 帧头 + 长度 + 命令码
        192, 168, 1, 100,                 // IP地址
        0x50, 0x1F,                       // 端口 8016 (小端序)
        0x01, 0x00,                       // 周期 1分钟 (小端序)
        0xCE                              // 帧尾
    };
    printf("\nTest 3: Set center1 command\n");
    uart_rx_handler(set_center1_cmd, sizeof(set_center1_cmd));
    
    // 测试4：读取中心1命令
    uint8_t read_center1_cmd[] = {0x7E, 0x02, 0x11, 0xCE};
    printf("\nTest 4: Read center1 command\n");
    uart_rx_handler(read_center1_cmd, sizeof(read_center1_cmd));
    
    // 测试5：设置厂家协议命令
    uint8_t set_protocol_cmd[] = {
        0x7E, 0x00, 0x06, 0x15,           // 帧头 + 长度 + 命令码
        0x01,                             // 厂家代码 (大连道盛)
        0x04,                             // 波特率代码 (9600)
        0x08,                             // 数据位 (8)
        0x01,                             // 停止位 (1)
        0x00,                             // 校验位 (无校验)
        0xCE                              // 帧尾
    };
    printf("\nTest 5: Set protocol command\n");
    uart_rx_handler(set_protocol_cmd, sizeof(set_protocol_cmd));
    
    // 测试6：读取厂家协议命令
    uint8_t read_protocol_cmd[] = {0x7E, 0x02, 0x15, 0xCE};
    printf("\nTest 6: Read protocol command\n");
    uart_rx_handler(read_protocol_cmd, sizeof(read_protocol_cmd));
    
    // 测试7：设置终端ID命令
    uint8_t set_terminal_id_cmd[] = {
        0x7E, 0x00, 0x07, 0x17,           // 帧头 + 长度 + 命令码
        0x52, 0x7B, 0x5A, 0x35, 0x0F,     // 终端ID 65322311666 (5字节小端序)
        0xCE                              // 帧尾
    };
    printf("\nTest 7: Set terminal ID command\n");
    uart_rx_handler(set_terminal_id_cmd, sizeof(set_terminal_id_cmd));
    
    // 测试8：读取终端ID命令
    uint8_t read_terminal_id_cmd[] = {0x7E, 0x02, 0x18, 0xCE};
    printf("\nTest 8: Read terminal ID command\n");
    uart_rx_handler(read_terminal_id_cmd, sizeof(read_terminal_id_cmd));
    
    return 0;
}

/**
 * STM32 HAL库集成示例
 */
#ifdef STM32_HAL_EXAMPLE

// UART接收缓冲区
#define RX_BUFFER_SIZE 256
uint8_t uart_rx_buffer[RX_BUFFER_SIZE];
volatile int uart_rx_index = 0;
volatile int uart_rx_complete = 0;

/**
 * UART接收完成中断回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) {
        uart_rx_complete = 1;
    }
}

/**
 * 在主循环中处理UART接收
 */
void uart_process_in_main_loop(void) {
    if (uart_rx_complete) {
        uart_rx_complete = 0;
        
        // 处理接收到的数据包
        pc_protocol_process(uart_rx_index, (char*)uart_rx_buffer);
        
        // 重新启动接收
        uart_rx_index = 0;
        HAL_UART_Receive_IT(&huart1, uart_rx_buffer, 1);
    }
}

/**
 * 发送响应函数的STM32实现
 */
void send_response(uint8_t* data, int length) {
    HAL_UART_Transmit(&huart1, data, length, 1000);
}

#endif // STM32_HAL_EXAMPLE
