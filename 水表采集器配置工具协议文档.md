# 水表采集器配置工具通信协议文档
## 1. 协议概述
### 1.1 协议特点
+ 基于串口通信（UART）
+ 采用固定帧格式
+ 支持CRC16校验
+ 小端序数据传输

### 1.2 通用帧格式

**升级功能帧格式（保留CRC校验）：**
```plain
| 帧头 | 长度高位 | 长度低位 | 命令码 | 数据域 | CRC低位 | CRC高位 | 帧尾 |
| 0x7E |   0x00   |   LEN    |  CMD   |  DATA  |  CRC_L  |  CRC_H  | 0xCE |
```

**其他功能帧格式（无CRC校验）：**
```plain
| 帧头 | 长度高位 | 长度低位 | 命令码 | 数据域 | 帧尾 |
| 0x7E |   0x00   |   LEN    |  CMD   |  DATA  | 0xCE |
```

**字段说明：**

+ 帧头：固定为 0x7E
+ 长度：数据域长度（不包含帧头、长度、帧尾）
+ 命令码：功能命令标识
+ 数据域：具体的配置数据
+ CRC：ModBus CRC16校验（仅升级功能使用，从长度字段开始计算）
+ 帧尾：固定为 0xCE

## 2. 系统基础功能
### 2.1 查询系统版本
**下发指令：**

```plain
7E 02 05 CE
```

+ 命令码：0x05
+ 数据长度：0字节
+ 功能：查询硬件模块状态和系统版本

**下发示例：**

```plain
十六进制：7E 02 05 CE
字节数组：[0x7E, 0x02, 0x05, 0xCE]
总长度：4字节
```

**期望回复：**

```plain
7E 04 85 VER1 VER2 VER3 CE
```

+ 响应码：0x85
+ 数据格式：3字节版本信息

**回复示例：**

```plain
返回：版本V1.2.3

十六进制：7E 04 85 31 32 33 CE
字节分解：
  7E        - 帧头
  04        - 长度(4字节)
  85        - 响应码(系统版本)
  31 32 33  - 版本字符("123"对应V1.2.3)
  CE        - 帧尾
```

### 2.2 连接握手/心跳
**下发指令（连接/心跳）：**

```plain
7E 02 02 CE
```

+ 命令码：0x02
+ 数据长度：0字节

**期望回复（连接确认）：**

```plain
7E 03 82 00 CE
```

+ 响应码：0x82
+ 状态：0x00 表示连接成功

**工具行为说明：**

+ 串口打开后，除“连接”按钮外，其它操作均为禁用状态。
+ 点击“连接”后，工具每1秒自动下发一次 `7E 02 02 CE`，直到收到 `7E 03 82 00 CE` 确认；收到后停止下发并将界面状态置为“已连接”，其后所有功能按钮才会启用。
+ 断开串口时，工具停止周期下发并重置为未连接状态。

## 3. 网络配置功能
### 3.1 上级平台配置
**数据格式说明：**

+ **IP地址**：4字节格式，布局为 `IP1 IP2 IP3 IP4`（完整4字节IP）
+ **端口号**：2字节格式，小端序（低字节在前，高字节在后）
+ **上报周期**：2字节，小端序，单位分钟，范围0001~9999分钟

#### 3.1.1 设置中心1
**下发指令：**

```plain
7E 00 09 11 IP1 IP2 IP3 IP4 PORT_L PORT_H PERIOD_L PERIOD_H CE
```

+ 命令码：0x11
+ 数据长度：9字节
+ IP1-IP4：IP地址的4个字节
+ PORT_L/H：端口号（小端序）
+ PERIOD_L/H：上报周期，单位分钟（小端序）

**下发示例：**

```plain
设置IP: *************, 端口: 8016, 上报周期: 1分钟

十六进制：7E 00 09 11 C0 A8 01 64 50 1F 01 00 CE
字节分解：
  7E        - 帧头
  00 09     - 长度(9字节)
  11        - 命令码(设置中心1)
  C0 A8 01 64 - IP地址(4字节格式，*************)
  50 1F     - 端口(8016 = 0x1F50, 小端2字节)
  01 00     - 上报周期(1分钟, 小端序)
  CE        - 帧尾
```

**期望回复：**

```plain
7E 03 83 00 CE
```

+ 响应码：0x83（配置设置成功）
+ 数据：1字节状态码（00=成功）

#### 3.1.2 读取中心1
**下发指令：**

```plain
7E 02 11 CE
```

+ 命令码：0x11（读取命令）

**下发示例：**

```plain
十六进制：7E 02 11 CE
字节数组：[0x7E, 0x02, 0x11, 0xCE]
总长度：4字节
```

**期望回复：**

```plain
7E 09 91 IP1 IP2 IP3 IP4 PORT_L PORT_H PERIOD_L PERIOD_H CE
```

+ 响应码：0x91
+ 数据格式同设置指令（4字节IP + 2字节端口 + 2字节周期）

**回复示例：**

```plain
返回IP: *************, 端口: 8016, 上报周期: 1分钟

十六进制：7E 09 91 C0 A8 01 64 50 1F 01 00 CE
字节分解：
  7E        - 帧头
  09        - 长度(9字节)
  91        - 响应码(中心1配置)
  C0 A8 01 64 - IP地址(4字节格式，*************)
  50 1F     - 端口(8016, 2字节格式)
  01 00     - 上报周期(1分钟)
  CE        - 帧尾
```

#### 3.1.3 其他平台配置
**中心2：**

+ 设置命令码：0x12
+ 读取命令码：0x12
+ 响应码：0x92

**下发示例（设置中心2）：**

```plain
设置IP: ********, 端口: 9000, 上报周期: 2分钟

十六进制：7E 00 09 12 0A 00 00 01 28 23 02 00 CE
字节分解：
  7E        - 帧头
  00 09     - 长度(9字节)
  12        - 命令码(设置中心2)
  0A 00 00 01 - IP地址(4字节格式，********)
  28 23     - 端口(9000, 2字节格式)
  02 00     - 上报周期(2分钟, 小端序)
  CE        - 帧尾

读取命令：7E 02 12 CE
```

**中心3：**

+ 设置命令码：0x13
+ 读取命令码：0x13
+ 响应码：0x93

**下发示例（设置中心3）：**

```plain
设置IP: ***********, 端口: 8888, 上报周期: 3分钟

十六进制：7E 00 09 13 AC 10 01 0A B8 22 03 00 CE
字节分解：
  7E        - 帧头
  00 09     - 长度(9字节)
  13        - 命令码(设置中心3)
  AC 10 01 0A - IP地址(4字节格式，***********)
  B8 22     - 端口(8888, 2字节格式)
  03 00     - 上报周期(3分钟, 小端序)
  CE        - 帧尾

读取命令：7E 02 13 CE
```



## 4. 厂家协议配置
### 4.1 设置厂家协议
**下发指令：**

```plain
7E 00 06 15 MANUFACTURER BAUD_CODE DATA_BITS STOP_BITS PARITY CE
```

**字段说明：**

+ 命令码：0x15
+ 数据长度：6字节（命令码1 + 厂家数据5）
+ MANUFACTURER_CODE：水表厂家代码，1字节，十进制，范围01~99
    - 01=大连道盛，02=泰安，03=唐山，04=河南
+ BAUD_RATE_CODE：波特率代码，1字节
    - 1=1200，2=2400，3=4800，4=9600，5=19200，6=38400
+ DATA_BITS：数据位，1字节，07=7位，08=8位，09=9位
+ STOP_BITS：停止位，1字节，1=1位，2=2位
+ PARITY：校验位，1字节，0=无校验，1=奇校验，2=偶校验

**实际发送示例：**

```plain
设置：大连道盛, 波特率9600, 数据位8, 停止位1, 无校验

完整数据包（10字节）：7E 00 06 15 01 04 08 01 00 CE

字节分解：
  7E        - 帧头
  00 06     - 长度(6字节，包含命令码到数据)
  15        - 命令码(设置厂家协议)
  01        - 厂家代码(01=大连道盛)
  04        - 波特率代码(04=9600)
  08        - 数据位(8)
  01        - 停止位(1)
  00        - 校验位(0=无校验)
  CE        - 帧尾

总长度：10字节
```

**其他波特率示例：**

```plain
01 ：1200
02 ：2400
03 ：4800
04 : 9600
05 : 19200
06 : 38400
```

**期望回复：**

```plain
7E 03 83 00 CE
```

+ 响应码：0x83（配置设置成功）
+ 数据：1字节状态码（00=成功）

### 4.2 读取厂家协议
**下发指令：**

```plain
7E 02 15 CE
```

**下发示例：**

```plain
十六进制：7E 02 15 CE
字节数组：[0x7E, 0x02, 0x15, 0xCE]
总长度：4字节
```

**期望回复：**

```plain
7E 07 95 MANUFACTURER BAUD_CODE DATA_BITS STOP_BITS PARITY CE
```

+ 响应码：0x95
+ 数据格式：1字节厂家代码 + 1字节波特率代码 + 1字节数据位 + 1字节停止位 + 1字节校验位

**回复示例：**

```plain
返回：大连道盛, 波特率9600, 数据位8, 停止位1, 无校验

十六进制：7E 07 95 01 04 08 01 00 CE
字节分解：
  7E        - 帧头
  07        - 长度(7字节)
  95        - 响应码(厂家协议配置)
  01        - 厂家代码(01=大连道盛)
  04        - 波特率代码(04=9600)
  08        - 数据位(8)
  01        - 停止位(1)
  00        - 校验位(0=无校验)
  CE        - 帧尾
```

**厂家代码说明：**

```plain
01 = 大连道盛
02 = 泰安
03 = 唐山
04 = 河南
```

**波特率代码说明：**

```plain
1 = 1200
2 = 2400
3 = 4800
4 = 9600
5 = 19200
6 = 38400
```

**校验位说明：**

```plain
0 = 无校验
1 = 奇校验
2 = 偶校验
```

**数据位说明：**

```plain
支持：5, 6, 7, 8 位
```

**停止位说明：**

```plain
支持：1, 2 位
```

## 5. 固件升级功能
### 5.1 升级开始
**下发指令：**

```plain
7E 00 0C 01 FILESIZE KEY1 KEY2 KEY3 KEY4 KEY5 KEY6 KEY7 KEY8 CE
```

+ 命令码：0x01
+ FILESIZE：文件大小（KB）
+ KEY1-8：8字节密钥（从文件头读取）

**下发示例：**

```plain
文件大小：32KB, 密钥：Alpha007

十六进制：7E 00 0C 01 20 41 6C 70 68 61 30 30 37 CE
字节分解：
  7E        - 帧头
  00 0C     - 长度(12字节)
  01        - 命令码(升级开始)
  20        - 文件大小(32KB)
  41 6C 70 68 61 30 30 37 - 密钥"Alpha007"的ASCII码
  CE        - 帧尾
```

### 5.2 数据传输
**下发指令：**

```plain
7E LEN_H LEN_L 03 SEQ_L SEQ_H DATA... CRC_L CRC_H CE
```

+ 命令码：0x03
+ SEQ：数据包序号（小端序）
+ DATA：最大1024字节数据

**下发示例：**

```plain
发送第1包数据（1024字节）

十六进制：7E 04 05 03 01 00 [1024字节数据] XX XX CE
字节分解：
  7E        - 帧头
  04 05     - 长度(1029字节: 命令码1 + 序号2 + 数据1024 + CRC2)
  03        - 命令码(数据传输)
  01 00     - 序号(1, 小端序)
  [数据]    - 1024字节固件数据
  XX XX     - CRC校验
  CE        - 帧尾

发送第2包数据（512字节）
十六进制：7E 02 03 03 02 00 [512字节数据] XX XX CE
```

### 5.3 升级结束
**下发指令：**

```plain
7E 00 04 05 00 CE
```

+ 命令码：0x05

**下发示例：**

```plain
十六进制：7E 00 04 05 00 CE
字节分解：
  7E        - 帧头
  00 04     - 长度(4字节)
  05        - 命令码(升级结束)
  00        - 数据(0)
  CE        - 帧尾
```

**期望回复：**

```plain
7E 05 06 00 CRC_L CRC_H CE
```

+ 响应码：0x06（升级成功）
+ 数据：1字节状态码（00=成功）

## 6. 其他配置功能
### 6.1 数据兼容模式配置
**下发指令：**

```plain
7E 00 03 16 DATA_COMPAT_MODE CE
```

**字段说明：**

+ 命令码：0x16
+ 数据长度：3字节（命令码1 + 数据1）
+ DATA_COMPAT_MODE：数据兼容模式，1字节
    - 00=关闭，01=开启

**实际发送示例：**

```plain
设置：数据兼容模式关闭

完整数据包（6字节）：7E 00 03 16 00 CE

字节分解：
  7E        - 帧头
  00 03     - 长度(3字节，包含命令码到数据)
  16        - 命令码(设置其他配置)
  00        - 数据兼容模式(00=关闭)
  CE        - 帧尾

总长度：6字节
```



### 6.2 读取数据兼容模式
**下发指令：**

```plain
7E 02 16 CE
```

**下发示例：**

```plain
十六进制：7E 02 16 CE
字节数组：[0x7E, 0x02, 0x16, 0xCE]
总长度：4字节
```

**期望回复：**

```plain
7E 03 96 DATA_COMPAT_MODE CE
```

+ 响应码：0x96
+ 数据格式：1字节数据兼容模式

**回复示例：**

```plain
返回：数据兼容模式关闭

十六进制：7E 03 96 00 CE
字节分解：
  7E        - 帧头
  03        - 长度(3字节)
  96        - 响应码(其他配置)
  00        - 数据兼容模式(00=关闭)
  CE        - 帧尾
```

**数据兼容模式说明：**

```plain
00 = 关闭
01 = 开启
```

---

## 7. CRC校验算法
采用ModBus CRC16校验算法：

+ 初始值：0xFFFF
+ 多项式：0xA001
+ 校验范围：从长度字段开始到数据域结束
+ 传输顺序：低字节在前，高字节在后



## 8. 终端ID配置
### 8.1 设置终端ID
**下发指令：**

```plain
7E 00 07 17 ID1 ID2 ID3 ID4 ID5 CE
```

**字段说明：**

+ 命令码：0x17
+ 数据长度：7字节（命令码1 + 终端ID5）
+ ID1-ID5：终端ID，5字节，小端序存储

**实际发送示例：**

```plain
设置终端ID：65322311666

完整数据包（10字节）：7E 00 07 17 52 7B 5A 35 0F CE

字节分解：
  7E        - 帧头
  00 07     - 长度(7字节，包含命令码到数据)
  17        - 命令码(设置终端ID)
  52 7B 5A 35 0F - 终端ID(65322311666, 5字节小端序)
  CE        - 帧尾

总长度：10字节
```

**期望回复：**

```plain
7E 03 83 00 CE
```

+ 响应码：0x83（配置设置成功）
+ 数据：1字节状态码（00=成功）

### 8.2 读取终端ID
**下发指令：**

```plain
7E 02 18 CE
```

**下发示例：**

```plain
十六进制：7E 02 18 CE
字节数组：[0x7E, 0x02, 0x18, 0xCE]
总长度：4字节
```

**期望回复：**

```plain
7E 07 97 ID1 ID2 ID3 ID4 ID5 CE
```

+ 响应码：0x97
+ 数据格式：5字节终端ID（小端序）

**回复示例：**

```plain
返回终端ID：65322311666

十六进制：7E 07 97 52 7B 5A 35 0F CE
字节分解：
  7E        - 帧头
  07        - 长度(7字节)
  97        - 响应码(终端ID配置)
  52 7B 5A 35 0F - 终端ID(65322311666, 5字节小端序)
  CE        - 帧尾
```

**终端ID说明：**

```plain
格式：11位数字
范围：00000000000 ~ 99999999999
存储：5字节小端序
示例：65322311666 -> 0x0F 0x35 0x5A 0x7B 0x52
```

