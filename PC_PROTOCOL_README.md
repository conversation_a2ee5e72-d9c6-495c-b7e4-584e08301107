# STM32 PC协议处理函数

这是一个用于STM32设备端的PC协议处理函数，实现了除升级功能外的所有协议功能。

## 文件说明

- `pc_protocol.h` - 协议处理头文件，包含结构体定义和函数声明
- `pc_protocol.c` - 协议处理实现文件，包含主要的协议解析和处理逻辑
- `pc_protocol_example.c` - 使用示例文件，展示如何集成和使用协议处理函数

## 支持的功能

1. **连接/心跳** - 命令码 0x02
2. **版本查询** - 命令码 0x05
3. **中心配置** - 命令码 0x11/0x12/0x13 (设置和读取中心1/2/3)
4. **厂家协议配置** - 命令码 0x15 (设置和读取)
5. **其他配置** - 命令码 0x16 (设置和读取数据兼容模式)
6. **终端ID配置** - 命令码 0x17/0x18 (设置和读取)

## 协议格式

### 通用帧格式（无CRC校验）
```
| 帧头 | 长度高位 | 长度低位 | 命令码 | 数据域 | 帧尾 |
| 0x7E |   0x00   |   LEN    |  CMD   |  DATA  | 0xCE |
```

### 数据格式说明

- **IP地址**: 4字节格式 `IP1 IP2 IP3 IP4`
- **端口号**: 2字节格式，小端序
- **上报周期**: 2字节，小端序，单位分钟
- **终端ID**: 5字节，小端序存储

## 使用方法

### 1. 包含头文件
```c
#include "pc_protocol.h"
```

### 2. 定义全局配置变量
```c
center_config_t g_center1_config;
center_config_t g_center2_config;
center_config_t g_center3_config;
protocol_config_t g_protocol_config;
other_config_t g_other_config;
terminal_id_config_t g_terminal_id_config;
char g_version[3] = {'1', '2', '3'};  // V1.2.3
```

### 3. 实现必要的函数
```c
// 发送响应函数
void send_response(uint8_t* data, int length) {
    // 通过UART发送数据
    HAL_UART_Transmit(&huart1, data, length, 1000);
}

// 配置保存函数（可选）
void save_center_config(uint8_t center_num, center_config_t* config) {
    // 保存到Flash等非易失性存储
}
```

### 4. 在UART接收中断中调用协议处理函数
```c
void uart_rx_handler(uint8_t* rx_buffer, int rx_length) {
    int result = pc_protocol_process(rx_length, (char*)rx_buffer);
    
    switch (result) {
        case 0:  // 成功处理
            break;
        case -1: // 数据包格式错误
            break;
        case -2: // 不支持的命令
            break;
    }
}
```

## 配置结构体说明

### 中心配置 (center_config_t)
```c
typedef struct {
    uint8_t ip[4];          // IP地址 4字节
    uint16_t port;          // 端口 2字节
    uint16_t period;        // 上报周期(分钟) 2字节
} center_config_t;
```

### 厂家协议配置 (protocol_config_t)
```c
typedef struct {
    uint8_t manufacturer;   // 厂家代码 1-99
    uint8_t baud_rate;      // 波特率代码 1-6
    uint8_t data_bits;      // 数据位 7-9
    uint8_t stop_bits;      // 停止位 1-2
    uint8_t parity;         // 校验位 0-2
} protocol_config_t;
```

### 其他配置 (other_config_t)
```c
typedef struct {
    uint8_t compat_mode;    // 数据兼容模式 0-关闭, 1-开启
} other_config_t;
```

### 终端ID配置 (terminal_id_config_t)
```c
typedef struct {
    uint64_t terminal_id;   // 终端ID (11位数字)
} terminal_id_config_t;
```

## 常量定义

### 厂家代码
- `MANUFACTURER_DALIAN = 1` - 大连道盛
- `MANUFACTURER_TAIAN = 2` - 泰安
- `MANUFACTURER_TANGSHAN = 3` - 唐山
- `MANUFACTURER_HENAN = 4` - 河南

### 波特率代码
- `BAUD_RATE_1200 = 1`
- `BAUD_RATE_2400 = 2`
- `BAUD_RATE_4800 = 3`
- `BAUD_RATE_9600 = 4`
- `BAUD_RATE_19200 = 5`
- `BAUD_RATE_38400 = 6`

### 数据位
- `DATA_BITS_7 = 7`
- `DATA_BITS_8 = 8`
- `DATA_BITS_9 = 9`

### 停止位
- `STOP_BITS_1 = 1`
- `STOP_BITS_2 = 2`

### 校验位
- `PARITY_NONE = 0` - 无校验
- `PARITY_ODD = 1` - 奇校验
- `PARITY_EVEN = 2` - 偶校验

## 协议示例

### 连接命令
```
发送: 7E 02 02 CE
响应: 7E 03 82 00 CE
```

### 版本查询
```
发送: 7E 02 05 CE
响应: 7E 04 85 31 32 33 CE  (版本V1.2.3)
```

### 设置中心1
```
发送: 7E 00 09 11 C0 A8 01 64 50 1F 01 00 CE
      (IP: *************, 端口: 8016, 周期: 1分钟)
响应: 7E 03 83 00 CE
```

### 读取中心1
```
发送: 7E 02 11 CE
响应: 7E 09 91 C0 A8 01 64 50 1F 01 00 CE
```

### 设置厂家协议
```
发送: 7E 00 06 15 01 04 08 01 00 CE
      (大连道盛, 9600, 8位, 1停止位, 无校验)
响应: 7E 03 83 00 CE
```

## 注意事项

1. **内存管理**: 确保全局配置变量有足够的内存空间
2. **中断安全**: 如果在中断中调用协议处理函数，注意中断安全
3. **Flash操作**: 配置保存函数中的Flash操作可能需要较长时间，注意不要在中断中执行
4. **错误处理**: 根据返回值进行适当的错误处理
5. **版本信息**: 记得设置正确的版本信息到 `g_version` 数组

## 移植说明

1. 根据实际硬件平台修改 `send_response` 函数
2. 实现配置保存和加载函数（如果需要持久化存储）
3. 根据实际需求调整缓冲区大小
4. 根据系统架构调整中断处理方式
