#include "pc_protocol.h"
#include <string.h>
#include <stdint.h>

// 协议常量定义
#define FRAME_HEAD          0x7E
#define FRAME_TAIL          0xCE
#define CMD_CONNECT         0x02
#define CMD_VERSION         0x05
#define CMD_SET_CENTER1     0x11
#define CMD_READ_CENTER1    0x11
#define CMD_SET_CENTER2     0x12
#define CMD_READ_CENTER2    0x12
#define CMD_SET_CENTER3     0x13
#define CMD_READ_CENTER3    0x13
#define CMD_SET_PROTOCOL    0x15
#define CMD_READ_PROTOCOL   0x15
#define CMD_SET_OTHER       0x16
#define CMD_READ_OTHER      0x16
#define CMD_SET_TERMINAL_ID 0x17
#define CMD_READ_TERMINAL_ID 0x18

// 响应命令码
#define RESP_CONNECT        0x82
#define RESP_CONFIG_OK      0x83
#define RESP_VERSION        0x85
#define RESP_CENTER1        0x91
#define RESP_CENTER2        0x92
#define RESP_CENTER3        0x93
#define RESP_PROTOCOL       0x95
#define RESP_OTHER          0x96
#define RESP_TERMINAL_ID    0x97

// 设备配置结构体
typedef struct {
    uint8_t ip[4];          // IP地址 4字节
    uint16_t port;          // 端口 2字节
    uint16_t period;        // 上报周期(分钟) 2字节
} center_config_t;

typedef struct {
    uint8_t manufacturer;   // 厂家代码
    uint8_t baud_rate;      // 波特率代码
    uint8_t data_bits;      // 数据位
    uint8_t stop_bits;      // 停止位
    uint8_t parity;         // 校验位
} protocol_config_t;

typedef struct {
    uint8_t compat_mode;    // 数据兼容模式
} other_config_t;

typedef struct {
    uint64_t terminal_id;   // 终端ID (11位数字，用64位存储)
} terminal_id_config_t;

// 全局配置变量（需要在其他地方定义和初始化）
extern center_config_t g_center1_config;
extern center_config_t g_center2_config;
extern center_config_t g_center3_config;
extern protocol_config_t g_protocol_config;
extern other_config_t g_other_config;
extern terminal_id_config_t g_terminal_id_config;
extern char g_version[3];  // 版本信息，如 "123" 表示 V1.2.3

// 发送响应函数声明（需要用户实现）
extern void send_response(uint8_t* data, int length);

// 构建响应帧
static int build_response_frame(uint8_t cmd, uint8_t* payload, int payload_len, uint8_t* output) {
    int index = 0;
    
    output[index++] = FRAME_HEAD;           // 帧头
    output[index++] = 0x00;                 // 长度高位
    output[index++] = payload_len + 1;      // 长度低位 (命令码 + 数据)
    output[index++] = cmd;                  // 命令码
    
    // 复制数据
    if (payload && payload_len > 0) {
        memcpy(&output[index], payload, payload_len);
        index += payload_len;
    }
    
    output[index++] = FRAME_TAIL;           // 帧尾
    
    return index;
}

// 处理连接/心跳命令
static void handle_connect_cmd(void) {
    uint8_t response[8];
    uint8_t payload = 0x00;  // 连接成功状态
    
    int len = build_response_frame(RESP_CONNECT, &payload, 1, response);
    send_response(response, len);
}

// 处理版本查询命令
static void handle_version_cmd(void) {
    uint8_t response[16];
    
    int len = build_response_frame(RESP_VERSION, (uint8_t*)g_version, 3, response);
    send_response(response, len);
}

// 处理设置中心配置命令
static void handle_set_center_cmd(uint8_t center_num, uint8_t* data) {
    center_config_t* config;
    
    // 选择对应的中心配置
    switch (center_num) {
        case 1: config = &g_center1_config; break;
        case 2: config = &g_center2_config; break;
        case 3: config = &g_center3_config; break;
        default: return;
    }
    
    // 解析数据：IP(4字节) + 端口(2字节) + 周期(2字节)
    memcpy(config->ip, data, 4);
    config->port = data[4] | (data[5] << 8);        // 小端序
    config->period = data[6] | (data[7] << 8);      // 小端序
    
    // 保存配置到Flash等非易失性存储（用户实现）
    // save_center_config(center_num, config);
    
    // 发送成功响应
    uint8_t response[8];
    uint8_t status = 0x00;  // 成功
    
    int len = build_response_frame(RESP_CONFIG_OK, &status, 1, response);
    send_response(response, len);
}

// 处理读取中心配置命令
static void handle_read_center_cmd(uint8_t center_num, uint8_t resp_cmd) {
    center_config_t* config;
    
    // 选择对应的中心配置
    switch (center_num) {
        case 1: config = &g_center1_config; break;
        case 2: config = &g_center2_config; break;
        case 3: config = &g_center3_config; break;
        default: return;
    }
    
    uint8_t response[32];
    uint8_t payload[8];
    
    // 构建响应数据：IP(4字节) + 端口(2字节) + 周期(2字节)
    memcpy(payload, config->ip, 4);
    payload[4] = config->port & 0xFF;           // 端口低字节
    payload[5] = (config->port >> 8) & 0xFF;    // 端口高字节
    payload[6] = config->period & 0xFF;         // 周期低字节
    payload[7] = (config->period >> 8) & 0xFF;  // 周期高字节
    
    int len = build_response_frame(resp_cmd, payload, 8, response);
    send_response(response, len);
}

// 处理设置厂家协议命令
static void handle_set_protocol_cmd(uint8_t* data) {
    // 解析数据：厂家代码(1字节) + 波特率(1字节) + 数据位(1字节) + 停止位(1字节) + 校验位(1字节)
    g_protocol_config.manufacturer = data[0];
    g_protocol_config.baud_rate = data[1];
    g_protocol_config.data_bits = data[2];
    g_protocol_config.stop_bits = data[3];
    g_protocol_config.parity = data[4];
    
    // 保存配置到Flash等非易失性存储（用户实现）
    // save_protocol_config(&g_protocol_config);
    
    // 发送成功响应
    uint8_t response[8];
    uint8_t status = 0x00;  // 成功
    
    int len = build_response_frame(RESP_CONFIG_OK, &status, 1, response);
    send_response(response, len);
}

// 处理读取厂家协议命令
static void handle_read_protocol_cmd(void) {
    uint8_t response[16];
    uint8_t payload[5];
    
    // 构建响应数据：厂家代码 + 波特率 + 数据位 + 停止位 + 校验位
    payload[0] = g_protocol_config.manufacturer;
    payload[1] = g_protocol_config.baud_rate;
    payload[2] = g_protocol_config.data_bits;
    payload[3] = g_protocol_config.stop_bits;
    payload[4] = g_protocol_config.parity;
    
    int len = build_response_frame(RESP_PROTOCOL, payload, 5, response);
    send_response(response, len);
}

// 处理设置其他配置命令
static void handle_set_other_cmd(uint8_t* data) {
    // 解析数据：数据兼容模式(1字节)
    g_other_config.compat_mode = data[0];
    
    // 保存配置到Flash等非易失性存储（用户实现）
    // save_other_config(&g_other_config);
    
    // 发送成功响应
    uint8_t response[8];
    uint8_t status = 0x00;  // 成功
    
    int len = build_response_frame(RESP_CONFIG_OK, &status, 1, response);
    send_response(response, len);
}

// 处理读取其他配置命令
static void handle_read_other_cmd(void) {
    uint8_t response[8];
    
    int len = build_response_frame(RESP_OTHER, &g_other_config.compat_mode, 1, response);
    send_response(response, len);
}

// 处理设置终端ID命令
static void handle_set_terminal_id_cmd(uint8_t* data) {
    // 解析数据：终端ID(5字节，小端序)
    g_terminal_id_config.terminal_id = 0;
    for (int i = 0; i < 5; i++) {
        g_terminal_id_config.terminal_id |= ((uint64_t)data[i]) << (i * 8);
    }
    
    // 保存配置到Flash等非易失性存储（用户实现）
    // save_terminal_id_config(&g_terminal_id_config);
    
    // 发送成功响应
    uint8_t response[8];
    uint8_t status = 0x00;  // 成功
    
    int len = build_response_frame(RESP_CONFIG_OK, &status, 1, response);
    send_response(response, len);
}

// 处理读取终端ID命令
static void handle_read_terminal_id_cmd(void) {
    uint8_t response[16];
    uint8_t payload[5];
    
    // 构建响应数据：终端ID(5字节，小端序)
    for (int i = 0; i < 5; i++) {
        payload[i] = (g_terminal_id_config.terminal_id >> (i * 8)) & 0xFF;
    }
    
    int len = build_response_frame(RESP_TERMINAL_ID, payload, 5, response);
    send_response(response, len);
}

/**
 * PC协议处理主函数
 * @param length 数据包长度
 * @param data 数据包内容
 * @return 0-成功处理, -1-数据包格式错误, -2-不支持的命令
 */
int pc_protocol_process(int length, char* data) {
    uint8_t* packet = (uint8_t*)data;
    
    // 最小数据包检查：帧头(1) + 长度(2) + 命令码(1) + 帧尾(1) = 5字节
    if (length < 5) {
        return -1;
    }
    
    // 检查帧头和帧尾
    if (packet[0] != FRAME_HEAD || packet[length-1] != FRAME_TAIL) {
        return -1;
    }
    
    // 解析长度字段
    uint16_t data_length = packet[1] << 8 | packet[2];
    
    // 检查数据包完整性
    if (length != data_length + 4) {  // 数据长度 + 帧头(1) + 长度(2) + 帧尾(1)
        return -1;
    }
    
    // 获取命令码
    uint8_t cmd = packet[3];
    uint8_t* cmd_data = &packet[4];  // 命令数据起始位置
    int cmd_data_len = data_length - 1;  // 命令数据长度（总数据长度 - 命令码）
    
    // 根据命令码处理不同命令
    switch (cmd) {
        case CMD_CONNECT:
            handle_connect_cmd();
            break;
            
        case CMD_VERSION:
            handle_version_cmd();
            break;
            
        case CMD_SET_CENTER1:
            if (cmd_data_len == 8) {  // IP(4) + 端口(2) + 周期(2)
                handle_set_center_cmd(1, cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_CENTER1:
            handle_read_center_cmd(1, RESP_CENTER1);
            break;
            
        case CMD_SET_CENTER2:
            if (cmd_data_len == 8) {
                handle_set_center_cmd(2, cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_CENTER2:
            handle_read_center_cmd(2, RESP_CENTER2);
            break;
            
        case CMD_SET_CENTER3:
            if (cmd_data_len == 8) {
                handle_set_center_cmd(3, cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_CENTER3:
            handle_read_center_cmd(3, RESP_CENTER3);
            break;
            
        case CMD_SET_PROTOCOL:
            if (cmd_data_len == 5) {  // 厂家(1) + 波特率(1) + 数据位(1) + 停止位(1) + 校验位(1)
                handle_set_protocol_cmd(cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_PROTOCOL:
            handle_read_protocol_cmd();
            break;
            
        case CMD_SET_OTHER:
            if (cmd_data_len == 1) {  // 兼容模式(1)
                handle_set_other_cmd(cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_OTHER:
            handle_read_other_cmd();
            break;
            
        case CMD_SET_TERMINAL_ID:
            if (cmd_data_len == 5) {  // 终端ID(5)
                handle_set_terminal_id_cmd(cmd_data);
            } else {
                return -1;
            }
            break;
            
        case CMD_READ_TERMINAL_ID:
            handle_read_terminal_id_cmd();
            break;
            
        default:
            return -2;  // 不支持的命令
    }
    
    return 0;  // 成功处理
}
