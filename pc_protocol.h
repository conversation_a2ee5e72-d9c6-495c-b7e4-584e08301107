#ifndef PC_PROTOCOL_H
#define PC_PROTOCOL_H

#include <stdint.h>

// 设备配置结构体定义
typedef struct {
    uint8_t ip[4];          // IP地址 4字节
    uint16_t port;          // 端口 2字节
    uint16_t period;        // 上报周期(分钟) 2字节
} center_config_t;

typedef struct {
    uint8_t manufacturer;   // 厂家代码 1-99
    uint8_t baud_rate;      // 波特率代码 1-6
    uint8_t data_bits;      // 数据位 7-9
    uint8_t stop_bits;      // 停止位 1-2
    uint8_t parity;         // 校验位 0-2
} protocol_config_t;

typedef struct {
    uint8_t compat_mode;    // 数据兼容模式 0-关闭, 1-开启
} other_config_t;

typedef struct {
    uint64_t terminal_id;   // 终端ID (11位数字)
} terminal_id_config_t;

// 厂家代码定义
#define MANUFACTURER_DALIAN     1   // 大连道盛
#define MANUFACTURER_TAIAN      2   // 泰安
#define MANUFACTURER_TANGSHAN   3   // 唐山
#define MANUFACTURER_HENAN      4   // 河南

// 波特率代码定义
#define BAUD_RATE_1200      1
#define BAUD_RATE_2400      2
#define BAUD_RATE_4800      3
#define BAUD_RATE_9600      4
#define BAUD_RATE_19200     5
#define BAUD_RATE_38400     6

// 数据位定义
#define DATA_BITS_7         7
#define DATA_BITS_8         8
#define DATA_BITS_9         9

// 停止位定义
#define STOP_BITS_1         1
#define STOP_BITS_2         2

// 校验位定义
#define PARITY_NONE         0   // 无校验
#define PARITY_ODD          1   // 奇校验
#define PARITY_EVEN         2   // 偶校验

// 兼容模式定义
#define COMPAT_MODE_OFF     0   // 关闭
#define COMPAT_MODE_ON      1   // 开启

// 函数声明
/**
 * PC协议处理主函数
 * @param length 数据包长度
 * @param data 数据包内容
 * @return 0-成功处理, -1-数据包格式错误, -2-不支持的命令
 */
int pc_protocol_process(int length, char* data);

/**
 * 发送响应函数（需要用户实现）
 * @param data 响应数据
 * @param length 响应数据长度
 */
extern void send_response(uint8_t* data, int length);

// 全局配置变量声明（需要用户在其他地方定义和初始化）
extern center_config_t g_center1_config;
extern center_config_t g_center2_config;
extern center_config_t g_center3_config;
extern protocol_config_t g_protocol_config;
extern other_config_t g_other_config;
extern terminal_id_config_t g_terminal_id_config;
extern char g_version[3];  // 版本信息，如 "123" 表示 V1.2.3

// 配置保存函数声明（需要用户实现）
/**
 * 保存中心配置到非易失性存储
 * @param center_num 中心编号 1-3
 * @param config 配置数据
 */
extern void save_center_config(uint8_t center_num, center_config_t* config);

/**
 * 保存厂家协议配置到非易失性存储
 * @param config 配置数据
 */
extern void save_protocol_config(protocol_config_t* config);

/**
 * 保存其他配置到非易失性存储
 * @param config 配置数据
 */
extern void save_other_config(other_config_t* config);

/**
 * 保存终端ID配置到非易失性存储
 * @param config 配置数据
 */
extern void save_terminal_id_config(terminal_id_config_t* config);

/**
 * 从非易失性存储加载所有配置
 */
extern void load_all_configs(void);

#endif // PC_PROTOCOL_H
